<template>
  <create-view :title="`${editMode ? '编辑' : '提交'}问题`" :goback="false" @goback="close">
    <div v-loading="loading" slot="content" class="question_wrap">
      <el-form ref="ruleForm" :model="formData" :rules="rules" label-position="left" label-width="120px">
        <el-card class="mb-10">
          <el-form-item label="标题" prop="title">
            <el-input v-model.trim="formData.title" placeholder="请输入"/>
          </el-form-item>
          <el-form-item label="类型" prop="type">
            <el-select v-model="formData.type" placeholder="请选择" filterable>
              <el-option
                v-for="item in questionTypeArr"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
          <el-form-item label="影响程度" prop="impactLevel">
            <el-select v-model="formData.impactLevel" placeholder="请选择" filterable>
              <el-option
                v-for="item in effectDegreeOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
        </el-card>
        <el-card class="mb-10">
          <el-form-item label="所属检测项目" prop="projectId">
            <el-tag
              v-if="formData.projectName"
              :disable-transitions="true"
              closable
              @click="openDrawer('testProject')"
              @close="tagClose('testProject')">
              {{ formData.projectName }}
            </el-tag>
            <el-button v-else type="ghost" @click="openDrawer('testProject')">请选择</el-button>
          </el-form-item>
          <el-form-item label="关联任务" prop="relatedTasksId">
            <el-tag
              v-if="formData.relatedTasksName"
              :disable-transitions="true"
              closable
              @click="openDrawer('taskProject')"
              @close="tagClose('taskProject')">
              {{ formData.relatedTasksName }}
            </el-tag>
            <el-button v-else type="ghost" @click="openDrawer('taskProject')">请选择</el-button>
          </el-form-item>
          <el-form-item label="关联用例" prop="relatedTestCasesId">
            <el-tag
              v-if="formData.relatedTestCasesName"
              :disable-transitions="true"
              closable
              @click="openDrawer('testCase')"
              @close="tagClose('testCase')">
              {{ formData.relatedTestCasesName }}
            </el-tag>
            <el-button v-else type="ghost" @click="openDrawer('testCase')">请选择</el-button>
          </el-form-item>
        </el-card>
        <el-card>
          <el-form-item v-if="formData.type != '1'" label="问题详情" prop="problemDetail">
            <myEditor
              :key="contentTimer"
              :content="formData.problemDetail"
              :editor-config="{
                placeholder: '请详细说明问题复现过程'
              }"
              id-prefix="content"
              width="100%"
              height="200px"
              @contentChange="contentChange('problemDetail', $event)"
            />
          </el-form-item>
          <!-- 漏洞问题start -->
          <div v-if="formData.type == '1'">
            <el-form-item label="漏洞类型" prop="loopholeType">
              <el-select v-model="formData.loopholeType" placeholder="请选择" filterable>
                <el-option
                  v-for="item in loopholeTypeOptions"
                  :key="item.lableId"
                  :label="item.lableName"
                  :value="item.lableId"
                />
              </el-select>
            </el-form-item>
            <el-form-item prop="loopholeLevel">
              <span slot="label">
                <span>漏洞等级</span>
                <el-tooltip transfer placement="top">
                  <img src="@/assets/images/question.png" style="width: 14px; height: 14px; margin-bottom: -2px;cursor:pointer" alt="" @click="openDrawer('loopholeLevelDetail')">
                  <div slot="content">
                    <div>查看漏洞等级说明</div>
                  </div>
                </el-tooltip>
              </span>
              <el-select v-model="formData.loopholeLevel" placeholder="请选择" filterable>
                <el-option
                  v-for="item in loopholeLevelOptions"
                  :key="item.lableId"
                  :label="item.lableName"
                  :value="item.lableId"
                />
              </el-select>
            </el-form-item>
            <el-form-item label="关联漏洞情报" prop="loopholeGen">
              <el-tag
                v-if="formData.loopholeGenName"
                :disable-transitions="true"
                closable
                @click="openDrawer('holeList')"
                @close="tagClose('holeList')">
                {{ formData.loopholeGenName }}
              </el-tag>
              <el-button v-else type="ghost" @click="openDrawer('holeList')">请选择</el-button>
            </el-form-item>
            <el-form-item label="漏洞地址" prop="loopholeAddress">
              <el-input v-model="formData.loopholeAddress" placeholder="请输入" maxlength="255"/>
            </el-form-item>
            <el-form-item label="漏洞详情" prop="problemDetail">
              <myEditor
                :key="contentTimer"
                :content="formData.problemDetail"
                :editor-config="{
                  placeholder: '请详细说明问题复现过程'
                }"
                id-prefix="content"
                width="100%"
                height="200px"
                @contentChange="contentChange('problemDetail', $event)"
              />
            </el-form-item>
            <el-form-item label="漏洞危害" prop="loopholeHazard">
              <myEditor
                :key="loopholeHazardTimer"
                :content="formData.loopholeHazard"
                :editor-config="{
                  placeholder: '请输入'
                }"
                id-prefix="content"
                width="100%"
                height="200px"
                @contentChange="contentChange('loopholeHazard', $event)"
              />
            </el-form-item>
          </div>
          <!-- 漏洞问题end -->
          <el-form-item label="附件" prop="fileList">
            <el-upload
              :headers="{ 'Admin-Token': token }"
              :http-request="httpRequest"
              :before-upload="handleUpload"
              :on-preview="handlePreview"
              :on-remove="handleRemove"
              :file-list="fileList"
              class="upload-demo1"
              action=""
              multiple
            >
              <el-button type="ghost">上传附件</el-button>
              <div slot="tip" class="el-upload__tip">
                单个附件大小不超过600MB
              </div>
            </el-upload>
          </el-form-item>
        </el-card>
        <!-- 侧拉弹窗 start -->
        <el-drawer
          :title="titleMapping[drawerName]"
          :visible.sync="drawerShow"
          :size="drawerName === 'testCase' ? '55%' : drawerWidth"
          append-to-body
          @close="drawerClose"
        >
          <transition name="el-fade-in-linear">
            <component
              :is="drawerName"
              :name="drawerName"
              :question-type="formData.questionType"
              :project-id="formData.projectId"
              :task-id="formData.relatedTasksId"
              :bug-type="formData.loopholeType"
              :bug-level="formData.loopholeLevel"
              @close="drawerClose"
              @call="drawerConfirmCall"
            />
          </transition>
        </el-drawer>
        <!-- 侧拉弹窗 end -->
      </el-form>
    </div>
    <div slot="footer">
      <el-button type="text" @click="close">取消</el-button>
      <el-button type="primary" @click="confirm('ruleForm')">确定</el-button>
    </div>
  </create-view>
</template>

<script>
import createView from '@/packages/create-view/index'
import myEditor from '@/packages/editor/index.vue'
import validate from '@/packages/validate'
import mixinsActionMenu from '@/packages/mixins/action_menu.js'
import module from '../config'
import testProject from '../components/testProject.vue'
import taskProject from '../components/taskProject.vue'
import testCase from '../components/testCase.vue'
import holeList from '../components/holeList.vue'
import loopholeLevelDetail from '../components/loopholeLevelDetail.vue'
import { listLabel } from '@/api/admin/label'
import { addQuestionApi, updateQuestionApi, getQuestionDetailApi, uploadQuestionFile } from '@/api/testing/questionManage.js'
import { testingItemsQueryPageAPI, getProblemTestTaskPage } from '@/api/testing/index'
import { getTestProcessesFileById, taskCaseDetail } from '@/api/testing/testCase.js'
export default {
  name: 'CreateUnFixedQuestion',
  components: {
    createView,
    myEditor,
    testProject,
    taskProject,
    testCase,
    holeList,
    loopholeLevelDetail
  },
  mixins: [mixinsActionMenu],
  data() {
    return {
      loading: false,
      questionId: this.$route.params.id || '',
      effectDegreeArr: module.effectDegreeArr, // 影响程度
      questionTypeArr: module.questionTypeArr, // 问题类型
      effectDegreeOptions: module.effectDegreeArr,
      contentTimer: 'content' + new Date().getTime(),
      loopholeHazardTimer: 'loopholeHazard' + new Date().getTime(),
      formData: {
        title: '',
        type: '',
        impactLevel: '',
        projectId: '',
        projectName: '',
        relatedTasksId: '',
        relatedTasksName: '',
        relatedTestCasesId: '',
        relatedTestCasesName: '',
        problemDetail: '<p>[步骤]</p><br><p>[结果]</p><br><p>[期望]</p><br>',
        loopholeType: '',
        loopholeLevel: '',
        loopholeGen: '', // 漏洞情报id
        loopholeGenName: '',
        loopholeHazard: '',
        attachmentIds: [],
        fileError: false
      },
      rules: {
        title: [validate.required('change'), validate.name_64_char],
        type: [validate.required('change')],
        impactLevel: [validate.required('change')],
        projectId: [validate.required('change')],
        relatedTasksId: [validate.required('change')],
        loopholeType: [validate.required('change')],
        loopholeLevel: [validate.required('change')],
        problemDetail: [validate.required()]
      },
      editMode: false,
      titleMapping: {
        'testProject': '选择项目',
        'taskProject': '选择任务',
        'testCase': '选择用例',
        'holeList': '选择漏洞',
        'loopholeLevelDetail': '查看漏洞等级说明'
      },
      projectOptions: [], // 项目
      taskOptions: [], // 任务
      loopholeTypeOptions: [], // 漏洞类型
      loopholeLevelOptions: [], // 漏洞等级
      fileList: [],
      currentFileIndex: -1
    }
  },
  created() {
    this.editMode = !!this.$route.params.id
  },
  async mounted() {
    // 当检测项目-问题清单/测试任务-问题清单点击提交问题才调用接口
    if (['testingTask_detail', 'testing_detail'].includes(this.$route.query.type)) {
      this.getProjectOptions()
      this.getTaskOptions()
    }
    this.getLoopholeTypeOptions()
    this.getLoopholeLevelOptions()
    // 编辑回显
    if (this.editMode) {
      getQuestionDetailApi({ id: this.questionId }).then((res) => {
        if (res.data && [0, 200].includes(res.code)) {
          this.formData = res.data
          if (res.data['loopholeLevel']) {
            this.formData['loopholeLevel'] = String(res.data['loopholeLevel'])
          }
          if (res.data['loopholeType']) {
            this.formData['loopholeType'] = String(res.data['loopholeType'])
          }
          // 强制刷新富文本编辑器
          this.contentTimer = 'content' + new Date().getTime()
          this.loopholeHazardTimer = 'loopholeHazard' + new Date().getTime()
        }
      })
      this.getTestProcessesFile()
    }
    if (this.$route.query.typeProblem == 'transferProblem') {
      const res = await taskCaseDetail(this.$route.query.taskCaseId)
      console.log(`🚀🚀🚀 - mounted - res:`, res)
      const steps = this.$route.query.steps || ''
      const expectedResult = this.$route.query.expectedResult || ''
      this.formData['problemDetail'] = `
        <p>[步骤]</p>
        ${steps}
        <p>[结果]</p>
        ${expectedResult}
        <p>[期望]</p>
        <br>
      `
      this.formData.relatedTestCasesId = this.$route.query.caseId
      this.formData.relatedTestCasesName = this.$route.query.caseName
    }
  },
  methods: {
    // 附件接口
    getTestProcessesFile() {
      const formData = new FormData()
      formData.append('sourceId', this.questionId)
      getTestProcessesFileById(formData).then(res => {
        this.fileList = res.data.map(item => ({
          name: item.name,
          status: 'success',
          url: item.path,
          id: item.id,
          uuid: item.id
        }))
        // 收集附件ID
        this.formData.attachmentIds = res.data.map(item => item.id)
      })
    },
    getProjectOptions() {
      const params = {
        pageType: 0
      }
      testingItemsQueryPageAPI(params).then(res => {
        this.projectOptions = res.data.data.records || []
        const projectItem = this.projectOptions.find(item => item.id == this.$route.query.projectId)
        this.$set(this.formData, 'projectName', projectItem.name)
        this.$set(this.formData, 'projectId', projectItem.id)
      })
    },
    getTaskOptions() {
      const params = {
        pageType: 0,
        projectId: this.$route.query.projectId
      }
      getProblemTestTaskPage(params).then(res => {
        this.taskOptions = res.data.data.records || []
        const taskItem = this.taskOptions.find(item => item.id == this.$route.query.taskId)
        this.$set(this.formData, 'relatedTasksName', taskItem.name)
        this.$set(this.formData, 'relatedTasksId', taskItem.id)
      })
    },
    getLoopholeTypeOptions() {
      const params = {
        page: 1,
        limit: 10000,
        lableId: 107,
        lableName: null
      }
      listLabel(params).then(res => {
        this.loopholeTypeOptions = res.rows || []
      })
    },
    getLoopholeLevelOptions() {
      const params = {
        page: 1,
        limit: 10000,
        lableId: 108,
        lableName: null
      }
      listLabel(params).then(res => {
        this.loopholeLevelOptions = res.rows || []
      })
    },
    drawerConfirmCall(type, data) {
      if (type == 'testProject') {
        this.$set(this.formData, 'projectId', data[0].id)
        this.$set(this.formData, 'projectName', data[0].projectName)
      } else if (type == 'taskProject') {
        this.$set(this.formData, 'relatedTasksId', data[0].id)
        this.$set(this.formData, 'relatedTasksName', data[0].name)
      } else if (type == 'testCase') {
        this.$set(this.formData, 'relatedTestCasesId', data[0].id)
        this.$set(this.formData, 'relatedTestCasesName', data[0].title)
      } else if (type == 'holeList') {
        this.$set(this.formData, 'loopholeGen', data[0].bugId)
        this.$set(this.formData, 'loopholeGenName', data[0].bugName)
      }
      this.drawerClose()
    },
    openDrawer(drawerName) {
      // 选择关联任务前先选择所属项目
      if (drawerName == 'taskProject' && !this.formData.projectId) {
        this.$message.error('请先选择所属检测项目')
        return
      }
      // 选择关联用例前先选择关联任务
      if (drawerName == 'testCase' && !this.formData.relatedTasksId) {
        this.$message.error('请先选择所属检测任务')
        return
      }
      this.drawerName = drawerName
    },
    tagClose(type) {
      if (type == 'testProject') {
        this.$set(this.formData, 'projectName', '')
        this.$set(this.formData, 'projectId', '')
        this.$set(this.formData, 'relatedTasksName', '')
        this.$set(this.formData, 'relatedTasksId', '')
        this.$set(this.formData, 'relatedTestCasesName', '')
        this.$set(this.formData, 'relatedTestCasesId', '')
      } else if (type == 'taskProject') {
        this.$set(this.formData, 'relatedTasksName', '')
        this.$set(this.formData, 'relatedTasksId', '')
        this.$set(this.formData, 'relatedTestCasesName', '')
        this.$set(this.formData, 'relatedTestCasesId', '')
      } else if (type == 'testCase') {
        this.$set(this.formData, 'relatedTestCasesName', '')
        this.$set(this.formData, 'relatedTestCasesId', '')
      } else if (type == 'holeList') {
        this.$set(this.formData, 'loopholeGen', '')
        this.$set(this.formData, 'loopholeGenName', '')
      }
      this.$forceUpdate()
    },
    // 富文本编辑器内容改变
    contentChange(key, value) {
      if (this.delHtml(value)) {
        this.formData[key] = value
      } else {
        const imgStrs = value.match(/<img.*?>/g)
        if (imgStrs && imgStrs.length) { // 内容只有图片时
          this.formData[key] = value
        } else {
          this.formData[key] = ''
        }
      }
      if (String(this.delHtml(value)).length != 0) {
        this.$refs['ruleForm'].validateField(key)
      }
    },
    // 过滤html代码、空格、回车等空白字符
    delHtml(str) {
      str = str.replace(/<("[^"]*"|'[^']*'|[^'">])*>/gi, '')
      str = str.replace(/[\r\n]/g, '')
      str = str.replace(/\s/g, '')
      str = str.replace(/&nbsp;/ig, '')
      return str
    },

    // 附件上传相关方法
    handleRemove(file) {
      // 从fileList中移除
      const index = this.fileList.findIndex(item => item.id === file.id)
      if (index !== -1) {
        this.fileList.splice(index, 1)
      }
    },
    handlePreview(file, index) {
      this.currentFileIndex = index
    },
    handleUpload(file) {
      if (file.size > 1024 * 1024 * 600) {
        this.$message.error(`文件大小超出限制（600MB）`)
        return false
      }
      const promise = new Promise(resolve => {
        this.$nextTick(function() {
          resolve(true)
        })
      })
      return promise
    },
    httpRequest(file) {
      const formData = new FormData()
      formData.append('sourceId', '')
      formData.append('source', 8)
      formData.append('name', file.file.name)
      formData.append('file', file.file)

      // 返回上传请求
      return uploadQuestionFile(formData)
        .then(res => {
          if (res.code === 0 || res.code === 200) {
            this.$message({
              message: '上传成功',
              type: 'success'
            })
            // 上传成功，添加到文件列表
            if (res.data && res.data.id) {
              // 检查是否已存在相同ID的文件，避免重复添加
              const existingFile = this.fileList.find(item => item.id === res.data.id)
              if (!existingFile) {
                this.fileList.push(res.data)
              }
            }
          } else {
            this.$message.error(res.msg || '上传失败')
          }
          return res
        })
        .catch(error => {
          this.$message.error('上传失败')
          return error
        })
    },
    close: function() {
      if (['testingTask_detail', 'testing_detail'].includes(this.$route.query.type)) {
        this.$router.push({
          name: 'unFixedQuestionList'
        })
      } else {
        this.$router.go(-1)
      }
    },
    // 提交表单
    confirm(formName) {
      this.$refs[formName].validate(valid => {
        if (valid) {
          this.loading = true
          const params = {
            title: this.formData.title,
            type: this.formData.type,
            impactLevel: this.formData.impactLevel,
            projectId: this.formData.projectId,
            relatedTasksId: this.formData.relatedTasksId,
            relatedTestCasesId: this.formData.relatedTestCasesId,
            problemDetail: this.formData.problemDetail,
            attachmentIds: this.fileList.map(file => file.id)
          }
          // 漏洞类型问题
          if (this.formData.type == '1') {
            params.loopholeType = this.formData.loopholeType
            params.loopholeLevel = this.formData.loopholeLevel
            params.loopholeGen = this.formData.loopholeGen
            params.loopholeAddress = this.formData.loopholeAddress
            params.loopholeHazard = this.formData.loopholeHazard
          }
          if (!this.editMode) {
            addQuestionApi(params).then((res) => {
              if ([0, 200].includes(res.code)) {
                this.$message.success('新增成功')
                if (['testingTask_detail', 'testing_detail'].includes(this.$route.query.type)) {
                  this.$router.push({
                    name: 'unFixedQuestionList'
                  })
                } else {
                  this.$emit('call', 'refresh')
                  this.$router.go(-1)
                }
              }
            }).finally(() => {
              this.loading = false
            })
          } else {
            params.id = this.questionId
            updateQuestionApi(params).then((res) => {
              if ([0, 200].includes(res.code)) {
                this.$message.success('编辑成功')
                this.$emit('call', 'refresh')
                this.$router.go(-1)
              }
            }).finally(() => {
              this.loading = false
            })
          }
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
// 去掉按 delete 键可删除提示
::v-deep .el-icon-close-tip {
  display: none !important;
}
.el-input {
  width: 400px;
}
.el-select {
  width: 400px;
}
.question_wrap {
  .mb-10 {
    margin-bottom: 10px;
  }
  .tpl-upload {
    .upload-select-input {
      display: none;
    }
    .upload-click-wrap {
      display: inline-block;
    }
    .file-container {
      overflow-wrap: break-word;
      word-break: normal;
      line-height: 1.5;
      border: 1px solid;
      margin-top: 5px;
      padding: 6px 10px;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      cursor: pointer;
      position: relative;
      .delete {
        position: absolute;
        top: 10px;
        right: 10px;
      }
    }
    .limit-text {
      font-size: 12px;
      color: var(--neutral-700);
      height: 32px;
      line-height: 32px;
    }
  }
}
</style>
