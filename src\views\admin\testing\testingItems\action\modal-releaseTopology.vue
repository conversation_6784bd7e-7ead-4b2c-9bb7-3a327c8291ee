<template>
  <div v-loading="loading" class="dialog-wrap">
    <el-alert :closable="false" type="warning">
      <div slot="title">此操作会删除虚拟设备的实例，以及释放物理设备的端口。请谨慎操作。</div>
    </el-alert>
    <el-alert :closable="false" class="resource-content" type="info">
      <div slot="title">
        <span class="mr-40">资源数量</span>
        <span>{{ num }}</span>
      </div>
    </el-alert>
    <div class="dialog-footer">
      <el-button type="text" @click="close">取消</el-button>
      <el-button :disabled="num == 0" type="primary" @click="confirm">确定</el-button>
    </div>
  </div>
</template>
<script>
import module from '../config.js'
import batchTemplate from '@/packages/batch-delete/modal-bat-template'
import modalMixins from '@/packages/mixins/modal_form'
import { releaseResourcesAPI } from '@/api/testing/index.js'

export default {
  name: 'Finish',
  components: { batchTemplate },
  mixins: [modalMixins],
  props: {
    data: {
      type: Array,
      default: () => {
        return []
      }
    },
    num: {
      type: Number,
      default: 0
    }
  },
  data() {
    return {
      moduleName: module.name,
      loading: false
    }
  },
  mounted() {
  },
  methods: {
    close: function() {
      this.$emit('close')
    },
    confirm: function() {
      this.loading = true
      releaseResourcesAPI(this.data[0].id).then(res => {
        if (res.data.code === 0 || res.data.code === 200) {
          this.$message.success('资源释放完成')
          this.$emit('call', 'refresh')
          this.close()
        }
      }).catch(() => {
        this.loading = false
      })
    }
  }
}
</script>
<style lang="less" scoped>
.resource-content {
  padding: 14px 16px;
}
</style>
